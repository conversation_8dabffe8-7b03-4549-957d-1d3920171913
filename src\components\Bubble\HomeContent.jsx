import AiChatbotIcon from '../Global/Icons/AiChatbotIcon';
import DButtonIcon from '../Global/DButtonIcon';
import UpRightIcon from '../Global/Icons/UpRightIcon';
import * as tasksService from '@/services/tabs.service';
import { useEffect, useState, useRef } from 'react';

import { usePreviousConversationsStore } from '@/stores/previousConversation/previousConversationStore';
import { DateTime } from 'luxon';
import { useConversationStore } from '@/stores/conversation/conversationStore';
import PoweredByDante from '../PoweredByDante';
import { useCustomizationStore } from '@/stores/customization/customizationStore';
import SendIcon from '../Global/Icons/SendIcon';
import DLoading from '../DLoading';

const HomeContent = ({
  kb_id,
  slots,
  isPreviewMode,
  token,
  setActiveTab,
  hidePoweredByDante,
  previous_conversation_enabled = true,
}) => {
  const [data, setData] = useState({});
  const [loading, setLoading] = useState(false);
  const { conversations, fetchConversations } = usePreviousConversationsStore();
  const { tabsCustomization, updateTabsCustomization } =
    useCustomizationStore();
  const [conversationsFromKbId, setConversationsFromKbId] = useState([]);
  const setCurrentConversation = useConversationStore(
    (state) => state.setCurrentConversation
  );

  // Video refs for auto-play functionality
  const videoRefs = useRef({});

  const getSlots = async () => {
    setLoading(true);
    try {
      const response = await tasksService.getAllSlots(kb_id, token);
      if (response.status === 200) {
        setData(response.data);
        updateTabsCustomization('home', response.data);
        setLoading(false);
      }
    } catch (e) {
      console.log(e);
    } finally {
      setTimeout(() => {
        setLoading(false);
      }, 1000);
    }
  };

  const handleConversationClick = (conversation_id, conversation) => {
    setCurrentConversation({ id: conversation_id, type: 'opening' });
    if (setActiveTab) {
      setActiveTab('chat');
    }
  };

  useEffect(() => {
    const updateData = () => {
      if (kb_id && !isPreviewMode) {
        const { home } = tabsCustomization;

        if (Object.keys(home).length === 0) {
          getSlots();
        } else {
          setData(home);
        }
      } else {
        setData(slots);
      }
    };

    updateData();
  }, [kb_id, slots]);

  useEffect(() => {
    fetchConversations();
  }, []);

  useEffect(() => {
    if (conversations[kb_id] && !isPreviewMode) {
      setConversationsFromKbId(
        conversations[kb_id]
          .sort((a, b) => b.createdAt - a.createdAt)
          .slice(0, 3)
      );
    } else if (isPreviewMode) {
      setConversationsFromKbId([
        {
          lastMessage: 'How to create an AI agent?',
          createdAt: DateTime.now().minus({ days: 2 }).toMillis(),
        },
        {
          lastMessage: 'How do I white-label my AI Chatbot?',
          createdAt: DateTime.now().minus({ days: 15 }).toMillis(),
        },
      ]);
    }
  }, [conversations]);

  // Video utility functions
  const getVideoEmbedUrl = (url) => {
    if (!url) return null;

    // YouTube URL patterns
    const youtubeRegex = /(?:youtube\.com\/(?:[^\/]+\/.+\/|(?:v|e(?:mbed)?)\/|.*[?&]v=)|youtu\.be\/)([^"&?\/\s]{11})/;
    const youtubeMatch = url.match(youtubeRegex);
    if (youtubeMatch) {
      return `https://www.youtube.com/embed/${youtubeMatch[1]}?autoplay=1&mute=1&controls=0&loop=1&playlist=${youtubeMatch[1]}`;
    }

    // Vimeo URL patterns
    const vimeoRegex = /(?:vimeo\.com\/)([0-9]+)/;
    const vimeoMatch = url.match(vimeoRegex);
    if (vimeoMatch) {
      return `https://player.vimeo.com/video/${vimeoMatch[1]}?autoplay=1&muted=1&controls=0&loop=1`;
    }

    // Facebook video patterns
    const facebookRegex = /(?:facebook\.com\/.*\/videos\/|fb\.watch\/)([0-9]+)/;
    const facebookMatch = url.match(facebookRegex);
    if (facebookMatch) {
      return `https://www.facebook.com/plugins/video.php?href=${encodeURIComponent(url)}&autoplay=true&muted=true`;
    }

    // Instagram video patterns (posts and reels)
    const instagramRegex = /(?:instagram\.com\/(?:p|reel)\/([A-Za-z0-9_-]+))/;
    const instagramMatch = url.match(instagramRegex);
    if (instagramMatch) {
      return `https://www.instagram.com/p/${instagramMatch[1]}/embed/`;
    }

    // TikTok video patterns
    const tiktokRegex = /(?:tiktok\.com\/.*\/video\/([0-9]+)|vm\.tiktok\.com\/([A-Za-z0-9]+))/;
    const tiktokMatch = url.match(tiktokRegex);
    if (tiktokMatch) {
      const videoId = tiktokMatch[1] || tiktokMatch[2];
      return `https://www.tiktok.com/embed/v2/${videoId}`;
    }

    // Twitch video patterns (videos and clips)
    const twitchVideoRegex = /(?:twitch\.tv\/videos\/([0-9]+))/;
    const twitchClipRegex = /(?:twitch\.tv\/.*\/clip\/([A-Za-z0-9_-]+)|clips\.twitch\.tv\/([A-Za-z0-9_-]+))/;
    const twitchVideoMatch = url.match(twitchVideoRegex);
    const twitchClipMatch = url.match(twitchClipRegex);

    if (twitchVideoMatch) {
      return `https://player.twitch.tv/?video=${twitchVideoMatch[1]}&parent=${window.location.hostname}&autoplay=true&muted=true`;
    }
    if (twitchClipMatch) {
      const clipId = twitchClipMatch[1] || twitchClipMatch[2];
      return `https://clips.twitch.tv/embed?clip=${clipId}&parent=${window.location.hostname}&autoplay=true&muted=true`;
    }

    // Dailymotion video patterns
    const dailymotionRegex = /(?:dailymotion\.com\/video\/([A-Za-z0-9]+))/;
    const dailymotionMatch = url.match(dailymotionRegex);
    if (dailymotionMatch) {
      return `https://www.dailymotion.com/embed/video/${dailymotionMatch[1]}?autoplay=1&mute=1`;
    }

    // Wistia video patterns
    const wistiaRegex = /(?:wistia\.com\/medias\/([A-Za-z0-9]+))/;
    const wistiaMatch = url.match(wistiaRegex);
    if (wistiaMatch) {
      return `https://fast.wistia.net/embed/iframe/${wistiaMatch[1]}?autoplay=1&muted=1`;
    }

    // Loom video patterns
    const loomRegex = /(?:loom\.com\/share\/([A-Za-z0-9]+))/;
    const loomMatch = url.match(loomRegex);
    if (loomMatch) {
      return `https://www.loom.com/embed/${loomMatch[1]}?autoplay=1&muted=1`;
    }

    // Jam Video patterns
    const jamVideoRegex = /(?:jam\.dev\/share\/([A-Za-z0-9]+))/;
    const jamVideoMatch = url.match(jamVideoRegex);
    if (jamVideoMatch) {
      return `https://jam.dev/embed/${jamVideoMatch[1]}?autoplay=1&muted=1`;
    }


    // Streamable video patterns
    const streamableRegex = /(?:streamable\.com\/([A-Za-z0-9]+))/;
    const streamableMatch = url.match(streamableRegex);
    if (streamableMatch) {
      return `https://streamable.com/e/${streamableMatch[1]}?autoplay=1&muted=1`;
    }

    // JW Player patterns
    const jwPlayerRegex = /(?:jwplatform\.com\/players\/([A-Za-z0-9_-]+))/;
    const jwPlayerMatch = url.match(jwPlayerRegex);
    if (jwPlayerMatch) {
      return `https://content.jwplatform.com/players/${jwPlayerMatch[1]}.html`;
    }

    // Brightcove video patterns
    const brightcoveRegex = /(?:players\.brightcove\.net\/([0-9]+)\/([A-Za-z0-9_-]+)\/index\.html\?videoId=([0-9]+))/;
    const brightcoveMatch = url.match(brightcoveRegex);
    if (brightcoveMatch) {
      return url; // Brightcove URLs are already embeddable
    }

    // For other video URLs, return as is
    return url;
  };

  // Enhanced function to detect if URL is a direct video file
  const isDirectVideoFile = (url) => {
    if (!url) return false;

    const videoExtensions = [
      '.mp4', '.webm', '.ogg', '.avi', '.mov', '.wmv', '.flv', '.mkv',
      '.m4v', '.3gp', '.ogv', '.m3u8', '.ts', '.mpd'
    ];

    const urlLower = url.toLowerCase();
    return videoExtensions.some(ext => urlLower.includes(ext)) ||
           urlLower.includes('blob:') ||
           urlLower.includes('data:video/');
  };

  const handleVideoClick = (url) => {
    window.open(url, '_blank');
  };

  // Helper function to detect video platform type
  const getVideoPlatform = (url) => {
    if (!url) return 'unknown';

    if (url.includes('youtube.com') || url.includes('youtu.be')) return 'youtube';
    if (url.includes('vimeo.com')) return 'vimeo';
    if (url.includes('facebook.com') || url.includes('fb.watch')) return 'facebook';
    if (url.includes('instagram.com')) return 'instagram';
    if (url.includes('tiktok.com')) return 'tiktok';
    if (url.includes('twitch.tv') || url.includes('clips.twitch.tv')) return 'twitch';
    if (url.includes('dailymotion.com')) return 'dailymotion';
    if (url.includes('wistia.com') || url.includes('wistia.net')) return 'wistia';
    if (url.includes('loom.com')) return 'loom';
    if 
    if (url.includes('streamable.com')) return 'streamable';
    if (url.includes('jwplatform.com')) return 'jwplayer';
    if (url.includes('brightcove.net')) return 'brightcove';

    // Check if it's a direct video file
    if (isDirectVideoFile(url)) return 'direct';

    return 'unknown';
  };

  // Enhanced function to get video aspect ratio
  const getVideoAspectRatio = (platform) => {
    switch (platform) {
      case 'tiktok':
      case 'instagram':
        return '9/16'; // Vertical videos
      case 'youtube':
      case 'vimeo':
      case 'dailymotion':
      case 'loom':
      case 'streamable':
      default:
        return '16/9'; // Standard widescreen
    }
  };

  // Enhanced auto-play logic for first video when data loads
  useEffect(() => {
    const videos = data?.videos || [];
    if (videos.length > 0) {
      // Small delay to ensure video elements are rendered
      setTimeout(() => {
        const firstVideo = videos[0];
        if (firstVideo) {
          const platform = getVideoPlatform(firstVideo.url);
          const videoRef = videoRefs.current[firstVideo.id];

          console.log('Attempting autoplay for:', firstVideo.title, 'Platform:', platform);

          // For iframe-based platforms, autoplay is handled by the embed URL parameters
          // For direct video elements, we can call play() method
          if (videoRef && videoRef.tagName === 'VIDEO') {
            // Ensure video is muted for autoplay compliance
            videoRef.muted = true;
            videoRef.currentTime = 0; // Start from beginning

            videoRef.play().catch(error => {
              console.error('Error auto-playing video:', error);
              // Fallback: try to load and play again
              setTimeout(() => {
                if (videoRef.readyState >= 2) { // HAVE_CURRENT_DATA
                  videoRef.play().catch(e => console.error('Retry autoplay failed:', e));
                }
              }, 500);
            });
          }

          // For iframe videos, autoplay is handled by URL parameters
          // but we can still log the attempt
          if (videoRef && videoRef.tagName === 'IFRAME') {
            console.log('Iframe video autoplay handled by embed URL parameters');
          }

          // Special handling for different platforms
          switch (platform) {
            case 'direct':
              if (videoRef && videoRef.tagName === 'VIDEO') {
                // Additional setup for direct video files
                videoRef.loop = true;
                videoRef.playsInline = true;
              }
              break;
            case 'youtube':
            case 'vimeo':
            case 'loom':
              // These platforms handle autoplay via embed URL parameters
              console.log(`${platform} autoplay configured via embed URL`);
              break;
            default:
              console.log(`Autoplay configured for platform: ${platform}`);
          }
        }
      }, 1200); // Slightly increased delay for better iframe loading
    }
  }, [data]);

  // Combine all arrays (quick_links, link_groups, sliders, meta_links, videos) into one array
  // Make sure to handle empty arrays gracefully
  const sections = [
    ...(data?.link_groups || []),
    ...(data?.quick_links || []),
    ...(data?.sliders || []),
    ...(data?.meta_links || []),
    ...(data?.videos || []),
  ];

  // Sort by order
  sections?.sort((a, b) => (a.order ?? 9999) - (b.order ?? 9999));

  // Helper functions to render each type
  const renderLinkGroup = (link_group) => {
    return (
      <div key={link_group.id} className="link-groups flex flex-col gap-size1">
        <p className="link-groups-title text-lg font-regular tracking-tight text-[var(--dt-color-element-100)] pl-size1">
          {link_group?.title ?? 'Quick links'}
        </p>
        <div className="link-groups-items flex flex-col gap-size2 mb-size1">
          {link_group?.items
            ?.sort((a, b) => a.order - b.order)
            .map((item) => (
              <a
                key={item.id}
                href={item.url}
                target="_blank"
                rel="noopener noreferrer"
                className="link-groups-item flex items-center justify-between bg-[var(--dt-color-surface-100)] shadow-sm border rounded-[10px] border-[var(--dt-color-surface-100)] p-size2 gap-size2"
              >
                <p className="link-groups-item-title text-base font-regular tracking-tight text-[var(--dt-color-element-100)]">
                  {item.name}
                </p>
                <DButtonIcon>
                  <UpRightIcon className="text-[var(--dt-color-element-100)]" />
                </DButtonIcon>
              </a>
            ))}
        </div>
      </div>
    );
  };

  const renderQuickLink = (item) => {
    return (
      <div
        key={item.id}
        className="quick-links flex items-center justify-between border rounded-[10px] border-[var(--dt-color-surface-100)] py-size2 px-size1 gap-size2 bg-[var(--dt-color-surface-100)] shadow-sm"
      >
        <a
          href={item.url}
          target={item?.open_in_new_tab ? '_blank' : '_self'}
          className="quick-links-item text-base font-regular tracking-tight text-[var(--dt-color-element-100)]"
        >
          {item.title}
        </a>
      </div>
    );
  };

  const renderSlider = (slider) => {
    return (
      <div className="slider flex flex-col gap-size1" key={slider.id}>
        <p className="slider-title text-lg font-regular tracking-tight text-[var(--dt-color-element-100)] pl-size1">
          {slider?.title ?? 'Updates'}
        </p>
        <div className="slider-items flex gap-size2 w-full overflow-x-auto no-scrollbar whitespace-nowrap">
          {slider?.items
            ?.sort((a, b) => a.order - b.order)
            .map((item) => (
              <div
                key={item.id}
                className="slider-item p-size1 flex-none flex flex-col gap-size2 w-[248px] bg-[var(--dt-color-surface-100)] border rounded-[10px] border-[var(--dt-color-surface-100)]"
              >
                <img
                  src={item.thumbnail_url}
                  className="slider-item-image h-[120px] object-cover"
                />
                <div className="slider-item-content flex flex-col gap-size0 w-full flex-wrap">
                  <a
                    className="slider-item-title text-sm font-regular text-wrap tracking-tight text-[var(--dt-color-element-75)] max-w-[240px]"
                    href={item.url}
                    target="_blank"
                  >
                    {item.name}
                  </a>
                  <p className="slider-item-description text-xs text-wrap text-[var(--dt-color-element-50)] max-w-[240px] tracking-tight leading-4">
                    {item.description.length > 100
                      ? item.description.substring(0, 150) + '...'
                      : item.description}
                  </p>
                </div>
              </div>
            ))}
        </div>
      </div>
    );
  };

  const renderMetaLink = (meta_link) => {
    return (
      <div
        className="meta-link border rounded-[10px] border-[var(--dt-color-surface-100)] p-size2 flex flex-col items-start gap-size2 bg-[var(--dt-color-surface-100)] shadow-sm"
        key={meta_link.id}
      >
        {meta_link?.image_url && <img src={meta_link?.image_url} />}
        <div className="meta-link-content flex flex-col gap-size0">
          <a
            href={meta_link?.url}
            target={meta_link?.open_in_new_tab ? '_blank' : '_self'}
            className="text-base font-regular tracking-tight text-[var(--dt-color-element-100)]"
          >
            {meta_link?.title}
          </a>
          <p className="text-xs font-light tracking-tight text-[var(--dt-color-element-50)]">
            {meta_link?.description}
          </p>
        </div>
      </div>
    );
  };

  const renderVideo = (item) => {
    const embedUrl = getVideoEmbedUrl(item.url);
    const platform = getVideoPlatform(item.url);

    // Enhanced platform detection for embeddable content
    const isEmbeddablePlatform = embedUrl && platform !== 'unknown' && platform !== 'direct' && (
      embedUrl.includes('youtube.com') ||
      embedUrl.includes('vimeo.com') ||
      embedUrl.includes('facebook.com') ||
      embedUrl.includes('instagram.com') ||
      embedUrl.includes('tiktok.com') ||
      embedUrl.includes('twitch.tv') ||
      embedUrl.includes('clips.twitch.tv') ||
      embedUrl.includes('dailymotion.com') ||
      embedUrl.includes('wistia.net') ||
      embedUrl.includes('loom.com') ||
      embedUrl.includes('streamable.com') ||
      embedUrl.includes('jwplatform.com') ||
      embedUrl.includes('brightcove.net')
    );

    // Determine video container height based on aspect ratio
    const getVideoHeight = () => {
      if (platform === 'tiktok' || platform === 'instagram') {
        return 'h-[300px]'; // Taller for vertical videos
      }
      return 'h-[200px]'; // Standard height for horizontal videos
    };

    return (
      <div
        key={item.id}
        className="video p-size1 flex flex-col gap-size2 w-full max-w-[90%] mx-auto"
        onClick={() => handleVideoClick(item.url)}
      >
        <div className="relative cursor-pointer">
          {isEmbeddablePlatform ? (
            // For supported platforms, use iframe
            <div className={`relative w-full ${getVideoHeight()} rounded-lg overflow-hidden bg-black/10`}>
              <iframe
                ref={(el) => {
                  if (el) videoRefs.current[item.id] = el;
                }}
                src={embedUrl}
                className="w-full h-full border-0"
                allow="autoplay; encrypted-media; fullscreen; picture-in-picture"
                allowFullScreen
                title={item.title || 'Video content'}
                loading="lazy"
                sandbox="allow-scripts allow-same-origin allow-presentation allow-popups"
                onError={(e) => {
                  console.error('Error loading video iframe:', e);
                  // Could add fallback logic here
                }}
              />
            </div>
          ) : (
            // For direct video files or when thumbnail is preferred
            <div className={`relative w-full ${getVideoHeight()} rounded-lg overflow-hidden bg-black/10`}>
              {item.show_thumbnail && item.thumbnail_url ? (
                <div className="relative w-full h-full">
                  <img
                    src={item.thumbnail_url}
                    alt={item.title || 'Video thumbnail'}
                    className="w-full h-full object-cover"
                    onError={(e) => {
                      console.error('Error loading thumbnail:', e);
                      e.target.style.display = 'none';
                    }}
                  />
                  {/* Play button overlay for thumbnail */}
                  <div className="absolute inset-0 flex items-center justify-center bg-black/20">
                    <div className="w-12 h-12 bg-white/90 rounded-full flex items-center justify-center">
                      <div className="w-0 h-0 border-l-[8px] border-l-black border-y-[6px] border-y-transparent ml-1"></div>
                    </div>
                  </div>
                </div>
              ) : (
                <video
                  ref={(el) => {
                    if (el) {
                      videoRefs.current[item.id] = el;
                    }
                  }}
                  src={item.url}
                  className="w-full h-full object-cover"
                  muted
                  loop
                  playsInline
                  controls
                  preload="metadata"
                  onError={(e) => {
                    console.error('Error loading video:', e);
                    // Could show error message or fallback
                  }}
                  onLoadedMetadata={() => {
                    // Video loaded successfully
                    console.log('Video loaded:', item.title);
                  }}
                />
              )}
            </div>
          )}
        </div>

        <div className="video-content flex flex-col gap-size0 w-full flex-wrap">
          <p className="video-title text-sm font-regular tracking-tight text-[var(--dt-color-element-75)]">
            {item.title || 'Untitled Video'}
          </p>
          {item.description && (
            <p className="video-description text-xs text-[var(--dt-color-element-50)] tracking-tight text-wrap">
              {item.description}
            </p>
          )}
          {/* Show platform indicator for debugging/info */}
          {platform !== 'unknown' && (
            <p className="text-xs text-[var(--dt-color-element-30)] tracking-tight capitalize">
              {platform} video
            </p>
          )}
        </div>
      </div>
    );
  };

  // if(loading && (!slots || !isPreviewMode)) {
  //   return <DLoading show={true} />
  // }

  return (
    <div className="flex flex-col gap-size3 mt-[35px] h-[calc(100%-35px)]">
      <span className="welcome-message text-xl font-regular tracking-tight text-[var(--dt-color-element-100)]"></span>
      {kb_id === '86927718-7690-4c0c-a99d-8bc8afda0a4c' && sections.filter((section) => section.type === 'quick_link').map((section) => {
        return (
          <div key={section.id}>
            {section.type === 'quick_link' && renderQuickLink(section)}
          </div>
        )
      })}
      <div
        className="send-message-container bg-[var(--dt-color-surface-100)] rounded-[10px] p-size2 flex items-center justify-between cursor-pointer"
        onClick={() => setActiveTab('chat')}
      >
        <p className="send-message-text text-[var(--dt-color-element-100)]">
          Send us a message
        </p>
        <DButtonIcon onClick={() => setActiveTab('chat')}>
          <SendIcon className="text-[var(--dt-color-element-100)] size-4" />
        </DButtonIcon>
      </div>
      {conversationsFromKbId.length > 0 && previous_conversation_enabled && (
        <div className="previous-conversations flex flex-col gap-size2">
          <p className="previous-conversations-text text-lg font-regular tracking-tight text-[var(--dt-color-element-100)]">
            Previous conversations
          </p>
          {conversationsFromKbId.map((conversation, index) => (
            <button
              disabled={isPreviewMode}
              key={index}
              className="flex text-left items-start border rounded-[10px] border-[var(--dt-color-surface-100)] p-size1 gap-size2 bg-[var(--dt-color-surface-100)] shadow-sm"
              onClick={() =>
                handleConversationClick(conversation.id, conversation)
              }
            >
              <div className="previous-conversations-icon w-4">
                <AiChatbotIcon
                  className={'mt-3 text-[var(--dt-color-brand-100)]'}
                />
              </div>
              <div className="previous-conversations-content flex flex-col  w-[calc(100%-40px)]">
                <p className="text-base font-regular tracking-tight text-[var(--dt-color-element-100)] truncate">
                  {conversation.lastMessage
                    ? conversation.lastMessage
                    : `Conversation - ${conversation.id}`}
                </p>
                <p className="previous-conversations-date text-xs font-light tracking-tight text-[var(--dt-color-element-50)]">
                  {DateTime.fromMillis(
                    conversation.createdAt ?? DateTime.now().toMillis(),
                    {
                      zone: 'utc',
                    }
                  ).toRelative()}
                </p>
              </div>
            </button>
          ))}
        </div>
      )}
      {sections.map((section) => {
        if (kb_id === '86927718-7690-4c0c-a99d-8bc8afda0a4c' && section.type === 'quick_link') {
          return null;
        }

        switch (section.type) {
          case 'quick_link':
            return renderQuickLink(section);
          case 'link_group':
            return renderLinkGroup(section);
          case 'slider':
            return renderSlider(section);
          case 'meta_link':
            return renderMetaLink(section);
          case 'video':
            return renderVideo(section);
          default:
            return null;
        }
      })}

      {!hidePoweredByDante && (
        <div className="flex gap-size0 items-center justify-center mt-auto pb-size2">
          <PoweredByDante variant="color" isPreviewMode={isPreviewMode} />
        </div>
      )}
    </div>
  );
};

export default HomeContent;
